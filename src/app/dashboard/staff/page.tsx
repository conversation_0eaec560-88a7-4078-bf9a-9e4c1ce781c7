'use client';

import { useState } from 'react';
import {
  Check,
  Clock,
  MoreHorizontal,
  Plus,
  Search,
  Shield,
  ShieldAlert,
  User,
} from 'lucide-react';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { InviteStaffDialog } from '@/components/blocks/invite-staff-dialog';
import { StaffProfileDialog } from '@/components/blocks/staff-profile-dialog';
import { ChangeRoleDialog } from '@/components/blocks/change-role-dialog';

// Sample staff data with proper typing
const staffMembers: StaffMember[] = [
  {
    id: 1,
    name: 'Dr. <PERSON> <PERSON>e',
    email: '<EMAIL>',
    role: 'Master Admin',
    status: 'active',
    joinedDate: 'Jan 15, 2023',
  },
  {
    id: 2,
    name: 'Dr. Sarah Smith',
    email: '<EMAIL>',
    role: 'Standard',
    status: 'active',
    joinedDate: 'Mar 22, 2023',
  },
  {
    id: 3,
    name: 'Dr. Michael Johnson',
    email: '<EMAIL>',
    role: 'Standard',
    status: 'active',
    joinedDate: 'Apr 5, 2023',
  },
  {
    id: 4,
    name: 'Dr. Emily Davis',
    email: '<EMAIL>',
    role: 'Standard',
    status: 'active',
    joinedDate: 'Jun 18, 2023',
  },
  {
    id: 5,
    name: 'Dr. Robert Wilson',
    email: '<EMAIL>',
    role: 'Standard',
    status: 'pending',
    joinedDate: 'May 12, 2023',
  },
];

interface StaffMember {
  id: number;
  name: string;
  email: string;
  role: string;
  status: 'active' | 'pending';
  joinedDate: string;
}

export default function StaffManagementPage() {
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [isProfileDialogOpen, setIsProfileDialogOpen] = useState(false);
  const [isRoleDialogOpen, setIsRoleDialogOpen] = useState(false);
  const [selectedStaff, setSelectedStaff] = useState<StaffMember | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const [staffList, setStaffList] = useState<StaffMember[]>(staffMembers);

  const filteredStaff = staffList.filter(
    (staff) =>
      staff.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      staff.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      staff.role.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  const handleRoleChange = (staffId: number, newRole: string) => {
    setStaffList((prevStaff) =>
      prevStaff.map((staff) =>
        staff.id === staffId ? { ...staff, role: newRole } : staff,
      ),
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-2xl font-bold tracking-tight">Staff Management</h1>
        <div className="mt-2 sm:mt-0">
          <Button onClick={() => setIsInviteDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" /> Invite Staff
          </Button>
        </div>
      </div>

      <Card design={false}>
        <CardHeader>
          <CardTitle>Staff Members</CardTitle>
          <CardDescription>
            Manage your clinic staff and their access levels
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4">
            <div className="flex w-full max-w-sm items-center space-x-2">
              <Input
                placeholder="Search staff..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="h-9"
              />
              <Button variant="outline" size="sm" className="h-9 px-4 shrink-0">
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
            </div>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Joined</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredStaff.map((staff) => (
                    <TableRow key={staff.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <Avatar className="h-8 w-8">
                            <AvatarImage
                              src={`https://xvatar.vercel.app/api/avatar/${staff.email}.svg?rounded=120&size=240`}
                            />
                            <AvatarFallback>
                              {staff.name
                                .split(' ')
                                .map((n) => n[0])
                                .join('')}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div>{staff.name}</div>
                            <div className="text-sm text-muted-foreground">
                              {staff.email}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {staff.role === 'Master Admin' ? (
                            <ShieldAlert className="size-5 text-rose-500" />
                          ) : (
                            <Shield className="size-5 text-blue-500" />
                          )}
                          {staff.role}
                        </div>
                      </TableCell>
                      <TableCell>
                        {staff.status === 'active' ? (
                          <Badge variant="success" className="px-4 py-1.5">
                            <Check className="mr-1 h-3 w-3" /> Active
                          </Badge>
                        ) : (
                          <Badge variant="pending" className="px-4 py-1.5">
                            <Clock className="mr-1 h-3 w-3" /> Pending
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell>{staff.joinedDate}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => {
                                setSelectedStaff(staff);
                                setIsProfileDialogOpen(true);
                              }}
                            >
                              <User className="mr-2 h-4 w-4" />
                              <span>View Profile</span>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => {
                                setSelectedStaff(staff);
                                setIsRoleDialogOpen(true);
                              }}
                            >
                              <Shield className="mr-2 h-4 w-4" />
                              <span>Change Role</span>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            {staff.status === 'pending' ? (
                              <DropdownMenuItem>
                                <Clock className="mr-2 h-4 w-4" />
                                <span>Resend Invitation</span>
                              </DropdownMenuItem>
                            ) : null}
                            <DropdownMenuItem className="text-red-600">
                              <span>Remove</span>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </CardContent>
      </Card>

      <InviteStaffDialog
        open={isInviteDialogOpen}
        onOpenChange={setIsInviteDialogOpen}
      />

      <StaffProfileDialog
        open={isProfileDialogOpen}
        onOpenChange={setIsProfileDialogOpen}
        staff={selectedStaff}
      />

      {selectedStaff && (
        <ChangeRoleDialog
          open={isRoleDialogOpen}
          onOpenChange={setIsRoleDialogOpen}
          staff={{
            id: selectedStaff.id,
            name: selectedStaff.name,
            currentRole: selectedStaff.role as
              | 'Master Admin'
              | 'Standard Admin',
          }}
          onRoleChange={(newRole) => {
            handleRoleChange(selectedStaff.id, newRole);
          }}
        />
      )}
    </div>
  );
}
