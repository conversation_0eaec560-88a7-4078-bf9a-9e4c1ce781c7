'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { OtpVerification } from '@/components/blocks/otp-verification';
import { toast } from 'sonner';
import { signIn, confirmSignIn, signOut } from 'aws-amplify/auth';
import '../.././../lib/amplify';
import { useAuth } from '@/contexts/AuthContext';

const formSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
});

export default function AdminLoginPage() {
  const router = useRouter();
  const [showOtpVerification, setShowOtpVerification] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [authFlow, setAuthFlow] = useState<'SIGNIN' | 'SIGNUP'>('SIGNIN');
  const { fetchUserDetails } = useAuth();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true);

    try {
      const { nextStep } = await signIn({
        username: values.email,
        options: {
          authFlowType: 'USER_AUTH',
          preferredChallenge: 'EMAIL_OTP',
        },
      });

      if (nextStep.signInStep === 'CONFIRM_SIGN_IN_WITH_EMAIL_CODE') {
        setAuthFlow('SIGNIN');
        setShowOtpVerification(true);
        toast.success('OTP Sent', {
          description: 'Verification code sent to your email',
        });
      } else {
        toast.error('Unhandled step', {
          description: nextStep.signInStep,
        });
      }
    } catch (error) {
      toast.error((error as Error).name, {
        description: (error as Error).message,
      });
    } finally {
      setIsLoading(false);
    }
  }

  const onOtpSubmit = async (otp: string) => {
    try {
      const response = await confirmSignIn({
        challengeResponse: otp,
      });

      if (response.isSignedIn) {
        // Fetch user details to check if admin
        const userDetails = await fetchUserDetails();

        if (userDetails && userDetails.isAdmin) {
          router.push('/admin/dashboard');
        } else {
          // Not an admin, sign out and show error
          try {
            await signOut();
          } catch (error) {
            console.error('Error signing out:', error);
          }

          toast.error('Access Denied', {
            description: 'You do not have admin privileges.',
          });
          router.push('/login');
        }
      }
    } catch (error) {
      console.error(error);
      toast.error((error as Error).name, {
        description: (error as Error).message,
      });
    }
  };

  function onOtpVerified() {
    // This is now handled in onOtpSubmit
  }

  return (
    <div className="flex min-h-screen items-center justify-center p-4">
      {showOtpVerification ? (
        <OtpVerification
          onVerified={onOtpVerified}
          email={form.getValues().email}
          authFlow={authFlow}
          onOtpSubmit={onOtpSubmit}
        />
      ) : (
        <Card className="w-full max-w-md">
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl font-bold">Admin Login</CardTitle>
            <CardDescription>
              Enter your email to receive a verification code for admin access
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? 'Sending OTP...' : 'Send OTP'}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
