'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import Link from 'next/link';
import robot from '@/assets/637d05fde12331fe02047e483cc84f91bfd5d0a8.png';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { OtpVerification } from '@/components/blocks/otp-verification';
import { toast } from 'sonner';
import { confirmSignIn, signIn, fetchAuthSession } from 'aws-amplify/auth';
import { getUserFromToken } from '@/utils/auth';
import Image from 'next/image';

const formSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
});

export default function LoginPage() {
  const router = useRouter();
  const [showOtpVerification, setShowOtpVerification] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true);

    try {
      const { nextStep } = await signIn({
        username: values.email,
        options: {
          authFlowType: 'USER_AUTH',
          preferredChallenge: 'EMAIL_OTP',
        },
      });

      if (nextStep.signInStep === 'CONFIRM_SIGN_IN_WITH_EMAIL_CODE') {
        setShowOtpVerification(true);
        toast.success('OTP Sent', {
          description: 'Verification code sent to your email',
        });
      } else {
        toast.error('Unhandled step', {
          description: nextStep.signInStep,
        });
      }
    } catch (error) {
      toast.error((error as Error).name, {
        description: (error as Error).message,
      });
    } finally {
      setIsLoading(false);
    }
  }

  const onOtpSubmit = async (otp: string) => {
    const response = await confirmSignIn({
      challengeResponse: otp,
    });
    if (response.isSignedIn) {
      onOtpVerified();
    }
  };

  async function onOtpVerified() {
    try {
      console.log('onOtpVerified - Starting verification process');

      // Add a small delay to ensure the auth state is properly updated
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Force a refresh of the auth state
      console.log('onOtpVerified - Fetching auth session');
      const session = await fetchAuthSession({ forceRefresh: true });

      console.log('onOtpVerified - Session data:', {
        hasSession: !!session,
        hasIdToken: !!session?.tokens?.idToken,
      });

      const idToken = session.tokens?.idToken?.toString();
      if (!idToken) {
        const error = new Error('No ID token found after verification');
        console.error('onOtpVerified - Error:', error);
        throw error;
      }

      // Get user details from the ID token
      const userDetails = getUserFromToken(idToken);
      console.log('onOtpVerified - User details from token:', userDetails);

      if (!userDetails) {
        throw new Error('Could not extract user details from token');
      }

      // Add a small delay to ensure state is updated before redirect
      await new Promise((resolve) => setTimeout(resolve, 300));

      // Redirect based on user role
      const redirectPath = userDetails.isAdmin
        ? '/admin/dashboard'
        : '/dashboard';
      console.log(`onOtpVerified - Redirecting to: ${redirectPath}`);
      router.push(redirectPath);
    } catch (error) {
      console.error('onOtpVerified - Error after OTP verification:', error);
      toast.error('Verification Error', {
        description:
          'There was an issue completing your verification. Please try again.',
      });

      // If there's an error, try to force a refresh of the auth state
      try {
        console.log(
          'onOtpVerified - Attempting to refresh auth state after error',
        );
        await fetchAuthSession({ forceRefresh: true });
      } catch (refreshError) {
        console.error(
          'onOtpVerified - Error refreshing auth state:',
          refreshError,
        );
      }
    }
  }

  return (
    <div className="min-h-dvh items-center grid md:grid-cols-2 grid-cols-1">
      <div className="absolute top-10 left-20 text-xl font-bold">
        <span className="text-primary">Smart</span>
        Reception
      </div>
      {showOtpVerification ? (
        <OtpVerification
          onVerified={onOtpVerified}
          email={form.getValues().email}
          authFlow="SIGNIN"
          onOtpSubmit={onOtpSubmit}
        />
      ) : (
        <div className="w-full mx-auto">
          <Card className="w-full max-w-md mx-auto">
            <CardHeader className="space-y-1">
              <CardTitle className="text-2xl font-bold">Login</CardTitle>
              <CardDescription>
                Enter your email to receive a verification code
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-4"
                >
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input placeholder="<EMAIL>" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading ? 'Sending OTP...' : 'Send OTP'}
                  </Button>
                </form>
              </Form>
              <div className="mt-4 text-center text-sm">
                New user?{' '}
                <Link href="/signup" className="text-primary hover:underline">
                  Sign up here
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
      <div className="h-dvh w-full hidden md:block overflow-hidden">
        <Image
          src={robot}
          alt="Logo"
          width={900}
          height={900}
          className="mx-auto object-center object-cover size-full"
        />
      </div>
    </div>
  );
}
