'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { OtpVerification } from '@/components/blocks/otp-verification';
import { toast } from 'sonner';
import {
  confirmSignIn,
  confirmSignUp,
  signIn,
  signUp,
  autoSignIn,
  fetchAuthSession,
} from 'aws-amplify/auth';
import { getUserFromToken } from '@/utils/auth';
import Image from 'next/image';

const formSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
});

export default function LoginPage() {
  const router = useRouter();
  const [showOtpVerification, setShowOtpVerification] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [authFlow, setAuthFlow] = useState<'SIGNUP' | 'SIGNIN'>('SIGNIN');

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
    },
  });

  // const initiateSignin = async (values: z.infer<typeof formSchema>) => {
  //   try {
  //     const { nextStep } = await signIn({
  //       username: values.email,
  //       options: {
  //         authFlowType: 'USER_AUTH',
  //         preferredChallenge: 'EMAIL_OTP',
  //       },
  //     });
  //     if (nextStep.signInStep === 'CONFIRM_SIGN_IN_WITH_EMAIL_CODE') {
  //       setAuthFlow('SIGNIN');
  //       setShowOtpVerification(true);
  //       toast.success('OTP Sent', {
  //         description: 'Verification code sent to your email',
  //       });
  //     } else {
  //       toast.error('Unhandled step', {
  //         description: nextStep.signInStep,
  //       });
  //     }
  //   } catch (error) {
  //     toast.error((error as Error).name, {
  //       description: (error as Error).message,
  //     });
  //   }
  // };

  // Try signup if login fails with user not found
  const initiateSignup = async (values: z.infer<typeof formSchema>) => {
    try {
      const { nextStep } = await signUp({
        username: values.email,
        options: {
          userAttributes: {
            email: values.email,
          },
          autoSignIn: {
            authFlowType: 'USER_AUTH',
          },
        },
      });
      if (nextStep.signUpStep === 'CONFIRM_SIGN_UP') {
        setAuthFlow('SIGNUP');
        setShowOtpVerification(true);
        toast.success('OTP Sent', {
          description:
            'Confirm your Signup by entering verification code sent to your email',
        });
      } else {
        toast.error('Unhandled step', {
          description: nextStep.signUpStep,
        });
      }
    } catch (error) {
      toast.error((error as Error).name, {
        description: (error as Error).message,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // https://docs.amplify.aws/react/build-a-backend/auth/connect-your-frontend/sign-in/
  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsLoading(true);

    // First try to sign in, assuming user already exists
    try {
      const { nextStep } = await signIn({
        username: values.email,
        options: {
          authFlowType: 'USER_AUTH',
          preferredChallenge: 'EMAIL_OTP',
        },
      });

      if (nextStep.signInStep === 'CONFIRM_SIGN_IN_WITH_EMAIL_CODE') {
        setAuthFlow('SIGNIN');
        setShowOtpVerification(true);
        toast.success('OTP Sent', {
          description: 'Verification code sent to your email',
        });
        setIsLoading(false);
      } else {
        toast.error('Unhandled step', {
          description: nextStep.signInStep,
        });
        setIsLoading(false);
      }
    } catch (error) {
      // If user doesn't exist, try to sign up
      if ((error as Error).name === 'UserNotFoundException') {
        void initiateSignup(values);
      } else {
        toast.error((error as Error).name, {
          description: (error as Error).message,
        });
        setIsLoading(false);
      }
    }
  }

  // https://docs.amplify.aws/react/build-a-backend/auth/connect-your-frontend/sign-in/
  const onOtpSubmit = async (otp: string) => {
    const email = form.getValues().email;
    if (authFlow === 'SIGNUP') {
      const { nextStep } = await confirmSignUp({
        username: email,
        confirmationCode: otp,
      });
      if (nextStep.signUpStep === 'COMPLETE_AUTO_SIGN_IN') {
        const response = await autoSignIn();
        if (response.isSignedIn) {
          onOtpVerified();
        }
      }
    } else if (authFlow === 'SIGNIN') {
      const response = await confirmSignIn({
        challengeResponse: otp,
      });
      if (response.isSignedIn) {
        onOtpVerified();
      }
    }
  };

  async function onOtpVerified() {
    try {
      console.log('onOtpVerified - Starting verification process');

      // Add a small delay to ensure the auth state is properly updated
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Force a refresh of the auth state
      console.log('onOtpVerified - Fetching auth session');
      const session = await fetchAuthSession({ forceRefresh: true });

      console.log('onOtpVerified - Session data:', {
        hasSession: !!session,
        hasIdToken: !!session?.tokens?.idToken,
      });

      const idToken = session.tokens?.idToken?.toString();
      if (!idToken) {
        const error = new Error('No ID token found after verification');
        console.error('onOtpVerified - Error:', error);
        throw error;
      }

      // Get user details from the ID token
      const userDetails = getUserFromToken(idToken);
      console.log('onOtpVerified - User details from token:', userDetails);

      if (!userDetails) {
        throw new Error('Could not extract user details from token');
      }

      // Add a small delay to ensure state is updated before redirect
      await new Promise((resolve) => setTimeout(resolve, 300));

      // Redirect based on user role
      const redirectPath = userDetails.isAdmin
        ? '/admin/dashboard'
        : '/dashboard';
      console.log(`onOtpVerified - Redirecting to: ${redirectPath}`);
      router.push(redirectPath);
    } catch (error) {
      console.error('onOtpVerified - Error after OTP verification:', error);
      toast.error('Verification Error', {
        description:
          'There was an issue completing your verification. Please try again.',
      });

      // If there's an error, try to force a refresh of the auth state
      try {
        console.log(
          'onOtpVerified - Attempting to refresh auth state after error',
        );
        await fetchAuthSession({ forceRefresh: true });
      } catch (refreshError) {
        console.error(
          'onOtpVerified - Error refreshing auth state:',
          refreshError,
        );
      }
    }
  }

  return (
    <div className="min-h-dvh items-center grid md:grid-cols-2 grid-cols-1">
      <div className="absolute top-10 left-20 text-xl font-bold">
        <span className="text-primary">Smart</span>
        Reception
      </div>
      {showOtpVerification ? (
        <OtpVerification
          onVerified={onOtpVerified}
          email={form.getValues().email}
          authFlow={authFlow}
          onOtpSubmit={onOtpSubmit}
        />
      ) : (
        <div className="w-full mx-auto">
          <Card className="w-full max-w-md mx-auto">
            <CardHeader className="space-y-1">
              <CardTitle className="text-2xl font-bold">Login</CardTitle>
              <CardDescription>
                Enter your email to receive a verification code
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-4"
                >
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input placeholder="<EMAIL>" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button type="submit" className="w-full" disabled={isLoading}>
                    {isLoading ? 'Sending OTP...' : 'Send OTP'}
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      )}
      <div className="h-dvh w-full hidden md:block overflow-hidden">
        <Image
          src="https://assets.lummi.ai/assets/QmW76V7KMExKPch37RabjdA8DukviiU2JD1RBdQineMS9w?auto=format&w=1500"
          alt="Logo"
          width={900}
          height={900}
          className="mx-auto object-top object-cover size-full"
        />
      </div>
    </div>
  );
}
