'use client';

import type React from 'react';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Bell,
  Building2,
  ChevronDown,
  ClipboardList,
  Home,
  LogOut,
  Menu,
  Phone,
  PhoneCall,
  Settings,
  Users,
  Shield,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Sheet, SheetContent } from '@/components/ui/sheet';
import { cn } from '@/lib/utils';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { useAuth } from '@/contexts/AuthContext';

interface AdminDashboardLayoutProps {
  children: React.ReactNode;
}

export default function AdminDashboardLayout({
  children,
}: AdminDashboardLayoutProps) {
  const pathname = usePathname();
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false);
  const { logout } = useAuth();

  const navigation = [
    { name: 'Admin Dashboard', href: '/admin/dashboard', icon: Home },
    { name: 'Clinics', href: '/admin/clinics', icon: Building2 },
    { name: 'Users', href: '/admin/users', icon: Users },
    { name: 'Call Analytics', href: '/admin/analytics', icon: Phone },
    { name: 'Twilio Numbers', href: '/admin/twilio-numbers', icon: PhoneCall },
    { name: 'Settings', href: '/admin/settings', icon: Settings },
  ];

  return (
    <ProtectedRoute requireAdmin={true}>
      <div className="flex h-screen bg-background">
        {/* Sidebar for desktop */}
        <div className="hidden md:flex md:w-64 md:flex-col">
          <div className="flex flex-col flex-grow border-r border-border bg-card pt-5">
            <div className="flex items-center flex-shrink-0 px-4">
              <span className="text-xl font-semibold text-foreground">
                Smart Reception
              </span>
              <Badge className="ml-2 bg-destructive">Admin</Badge>
            </div>
            <div className="mt-5 flex-grow flex flex-col">
              <nav className="flex-1 space-y-1 px-2">
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={cn(
                      pathname === item.href
                        ? 'bg-accent/80 text-accent-foreground'
                        : 'text-muted-foreground hover:bg-accent/40 hover:text-secondary-foreground',
                      'group flex items-center px-2 py-2 text-sm font-medium rounded-lg',
                    )}
                  >
                    <item.icon
                      className={cn(
                        pathname === item.href
                          ? 'text-primary'
                          : 'text-muted-foreground group-hover:text-foreground',
                        'mr-3 h-5 w-5 flex-shrink-0',
                      )}
                      aria-hidden="true"
                    />
                    {item.name}
                  </Link>
                ))}
              </nav>
            </div>
            <div className="flex-shrink-0 flex border-t border-border p-4">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className="flex items-center w-full justify-start px-2"
                  >
                    <div className="flex items-center">
                      <Avatar className="h-8 w-8 mr-2">
                        <AvatarImage src="/placeholder-user.jpg" alt="User" />
                        <AvatarFallback>AD</AvatarFallback>
                      </Avatar>
                      <div className="text-sm text-left mr-2">
                        <div className="font-medium text-foreground">
                          Admin User
                        </div>
                        <div className="text-xs text-muted-foreground">
                          Super Admin
                        </div>
                      </div>
                      <ChevronDown className="h-4 w-4 ml-auto text-muted-foreground" />
                    </div>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel>Admin Account</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    <Shield className="mr-2 h-4 w-4" />
                    <span>Admin Profile</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Settings className="mr-2 h-4 w-4" />
                    <span>Settings</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={logout}>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Log out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>

        {/* Mobile navigation */}
        <Sheet open={isMobileNavOpen} onOpenChange={setIsMobileNavOpen}>
          <SheetContent side="left" className="w-64 p-0">
            <div className="flex flex-col h-full">
              <div className="flex items-center justify-between p-4 border-b border-border">
                <span className="text-xl font-semibold text-foreground">
                  Smart Reception
                </span>
                <Badge className="bg-destructive">Admin</Badge>
              </div>
              <nav className="flex-1 space-y-1 p-2">
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={cn(
                      pathname === item.href
                        ? 'bg-accent text-accent-foreground'
                        : 'text-muted-foreground hover:bg-secondary hover:text-secondary-foreground',
                      'group flex items-center px-2 py-2 text-sm font-medium rounded-md',
                    )}
                    onClick={() => setIsMobileNavOpen(false)}
                  >
                    <item.icon
                      className={cn(
                        pathname === item.href
                          ? 'text-primary'
                          : 'text-muted-foreground group-hover:text-foreground',
                        'mr-3 h-5 w-5 flex-shrink-0',
                      )}
                      aria-hidden="true"
                    />
                    {item.name}
                  </Link>
                ))}
              </nav>
              <div className="flex-shrink-0 flex border-t border-border p-4">
                <div className="flex items-center w-full">
                  <Avatar className="h-8 w-8 mr-2">
                    <AvatarImage src="/placeholder-user.jpg" alt="User" />
                    <AvatarFallback>AD</AvatarFallback>
                  </Avatar>
                  <div className="text-sm">
                    <div className="font-medium text-foreground">
                      Admin User
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Super Admin
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </SheetContent>
        </Sheet>

        {/* Main content */}
        <div className="flex flex-1 flex-col overflow-hidden">
          {/* Top navigation */}
          <div className="bg-card shadow-sm z-10">
            <div className="flex h-16 items-center justify-between px-4">
              <div className="flex">
                <Button
                  variant="ghost"
                  size="icon"
                  className="md:hidden"
                  onClick={() => setIsMobileNavOpen(true)}
                >
                  <Menu className="h-6 w-6" />
                </Button>
              </div>
              <div className="flex items-center space-x-4">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="relative">
                      <Bell className="h-5 w-5" />
                      <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center bg-destructive">
                        3
                      </Badge>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-80">
                    <DropdownMenuLabel>Notifications</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <div className="max-h-80 overflow-y-auto">
                      {[1, 2, 3].map((i) => (
                        <DropdownMenuItem key={i} className="py-2">
                          <div className="flex items-start">
                            <ClipboardList className="h-5 w-5 mr-2 mt-0.5 text-muted-foreground" />
                            <div>
                              <p className="font-medium text-foreground">
                                New clinic registered
                              </p>
                              <p className="text-xs text-muted-foreground">
                                A new clinic has registered on the platform
                              </p>
                              <p className="text-xs text-muted-foreground/70 mt-1">
                                {i === 1
                                  ? 'Just now'
                                  : i === 2
                                    ? '2 hours ago'
                                    : 'Yesterday'}
                              </p>
                            </div>
                          </div>
                        </DropdownMenuItem>
                      ))}
                    </div>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem className="justify-center">
                      <Button variant="ghost" className="w-full">
                        View all notifications
                      </Button>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
                <div className="md:hidden">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src="/placeholder-user.jpg" alt="User" />
                    <AvatarFallback>AD</AvatarFallback>
                  </Avatar>
                </div>
              </div>
            </div>
          </div>

          {/* Page content */}
          <main className="flex-1 overflow-y-auto p-4 md:p-6">{children}</main>
        </div>
      </div>
    </ProtectedRoute>
  );
}
