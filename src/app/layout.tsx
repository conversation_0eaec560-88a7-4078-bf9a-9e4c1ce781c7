import type { <PERSON>ada<PERSON> } from 'next';
import { D<PERSON>_Sans, <PERSON>eist_Mono } from 'next/font/google';
import { Toaster } from '@/components/ui/sonner';
import { AuthProvider } from '@/contexts/AuthContext';
import './globals.css';
import { ThemeProvider } from '@/contexts/ThemeProvider';

const dmSans = DM_Sans({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'Smart Reception – AI Voice Agent for Clinics',
  description:
    'Automate patient calls with AIVA – an intelligent, empathetic voice agent for healthcare clinics. Built with Retell AI, Next.js, and Node.js.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${dmSans.className} ${geistMono.variable} antialiased`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
        >
          <Toaster position="top-center" richColors theme="dark" />
          <AuthProvider>{children}</AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
