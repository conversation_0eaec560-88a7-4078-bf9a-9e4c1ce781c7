'use client';

import { API_ENDPOINTS } from '@/actions';
import { getCurrentUser, fetchUserAttributes } from 'aws-amplify/auth';

export interface UserDetails {
  uid: string;
  email: string;
  role?: string;
  isAdmin: boolean;
}

export interface UserProfile {
  _id: string;
  first_name: string;
  last_name: string;
  email: string;
  uid: string;
  created_at: string;
  updated_at: string;
  __v: number;
}

export interface UserProfileResponse {
  data: UserProfile;
}

/**
 * Fetches the current authenticated user details from AWS Cognito
 */
export async function getAuthenticatedUser(): Promise<UserDetails | null> {
  try {
    // Get current user from Amplify
    const currentUser = await getCurrentUser();
    const userAttributes = await fetchUserAttributes();

    return {
      uid: currentUser.userId,
      email: userAttributes.email || '',
      isAdmin: false, // Default value, will be updated by getUserDetails
    };
  } catch (error) {
    console.error('Error getting authenticated user:', error);
    return null;
  }
}

/**
 * Fetches user details including role from the backend API
 * @param token Access token for authorization
 */
export async function getUserDetails(
  token: string,
): Promise<UserDetails | null> {
  try {
    const response = await fetch(API_ENDPOINTS.USER_PROFILE, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch user details: ${response.status}`);
    }

    const data = await response.json();

    // Extract user details from response
    const userDetails: UserDetails = {
      uid: data.uid || '',
      email: data.email || '',
      role: data.role || '',
      isAdmin: data.role === 'admin',
    };

    return userDetails;
  } catch (error) {
    console.error('Error fetching user details:', error);
    return null;
  }
}
