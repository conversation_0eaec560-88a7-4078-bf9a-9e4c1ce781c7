'use client';

import { API_ENDPOINTS } from '@/actions';
import { ClinicType } from '@/lib/types';

export interface ClinicCreationData {
  clinic_name: string;
  clinic_email: string;
  clinic_phone?: string;
  clinic_website: string;
}

export interface ClinicCreationResponse {
  data: ClinicType;
}

/**
 * Creates a new clinic in the backend
 * @param clinicData Clinic data containing required fields
 * @param accessToken Cognito access token
 * @returns Promise with the clinic creation response
 */
export async function createClinic(
  clinicData: ClinicCreationData,
  accessToken: string,
): Promise<{
  ok: boolean;
  status: number;
  data?: ClinicCreationResponse;
  error?: string;
}> {
  try {
    const response = await fetch(API_ENDPOINTS.CLINICS, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify(clinicData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        ok: false,
        status: response.status,
        error: errorData.message || 'Failed to create clinic',
      };
    }

    const data = await response.json();
    return {
      ok: true,
      status: response.status,
      data,
    };
  } catch (error) {
    console.error('Error creating clinic:', error);
    return {
      ok: false,
      status: 500,
      error: 'Network error occurred while creating clinic',
    };
  }
}
