'use server';

import { UserProfile, UserProfileResponse } from '@/services/userService';
import { API_ENDPOINTS } from '.';

/**
 * Fetches the complete user profile from the backend API
 * @param token Access token for authorization
 * @returns Promise with the user profile response
 */
export async function getUserProfile(token: string): Promise<{
  ok: boolean;
  status: number;
  data?: UserProfile;
  error?: string;
}> {
  try {
    const response = await fetch(API_ENDPOINTS.USER_PROFILE, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
      next: {
        revalidate: 18000,
        tags: ['user-profile'],
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        ok: false,
        status: response.status,
        error: errorData.message || 'Failed to fetch user profile',
      };
    }

    const profileResponse: UserProfileResponse = await response.json();
    return {
      ok: true,
      status: response.status,
      data: profileResponse.data,
    };
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return {
      ok: false,
      status: 500,
      error: 'Network error occurred while fetching user profile',
    };
  }
}
