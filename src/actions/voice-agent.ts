'use server';

import { VoiceAgent, ApiResponse } from '@/lib/agent-types';
import { API_ENDPOINTS, QueryParams } from '.';

export async function getVoiceAgent(
  clinicId: string,
  token: string,
): Promise<ApiResponse<VoiceAgent>> {
  try {
    const url = new URL(API_ENDPOINTS.RETELL_AGENT);
    url.searchParams.set(QueryParams.CLINIC_ID, clinicId);

    const response = await fetch(url.toString(), {
      headers: {
        Authorization: `Bearer ${token}`,
      },
      next: {
        revalidate: 6000,
        tags: [`voice-agent-${clinicId}`],
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        ok: false,
        error: errorData.message || 'Failed to fetch voice agent data',
      };
    }

    const data = (await response.json()) as VoiceAgent;
    return {
      ok: true,
      data,
    };
  } catch {
    return {
      ok: false,
      error: 'An unexpected error occurred',
    };
  }
}
