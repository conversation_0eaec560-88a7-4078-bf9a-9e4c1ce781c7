'use client';

import {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
  useRef,
  ReactNode,
} from 'react';
import '@/lib/amplify';
import { AuthSession, getCurrentUser, signOut } from 'aws-amplify/auth';
import { fetchAuthSession } from 'aws-amplify/auth';
import { UserDetails } from '@/services/userService';

interface AuthContextType {
  isAuthenticated: boolean;
  isAdmin: boolean;
  loading: boolean;
  userDetails: UserDetails | null;
  logout: () => void;
  checkAuth: () => Promise<boolean>;
  fetchUserDetails: () => Promise<UserDetails | null>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [userDetails, setUserDetails] = useState<UserDetails | null>(null);
  const hasInitialized = useRef<boolean>(false);

  const checkIsAdmin = (session: AuthSession): boolean => {
    try {
      const accessToken = session.tokens?.accessToken;
      if (!accessToken) return false;

      const payload = accessToken.payload;
      const scope = payload?.['cognito:groups'] as Array<string> | undefined;

      return scope?.includes('admin') || false;
    } catch (error) {
      console.error('Error checking admin status:', error);
      return false;
    }
  };

  // Function to fetch user details from the API
  const fetchUserDetails = async (): Promise<UserDetails | null> => {
    try {
      const session = await fetchAuthSession();
      console.log('Auth check - Session:', session);
      if (!session.tokens?.accessToken) {
        return null;
      }
      const details = await getCurrentUser();
      const isAdminUser = checkIsAdmin(session);

      return {
        uid: details.userId,
        email: details.signInDetails?.loginId || '',
        isAdmin: isAdminUser,
        role: isAdminUser ? 'admin' : 'user',
      };
    } catch (error) {
      console.error('Error fetching user details:', error);
      return null;
    }
  };

  // Function to check authentication status
  const checkAuth = useCallback(async (): Promise<boolean> => {
    setLoading(true);

    try {
      // Get session from Amplify
      const session = await fetchAuthSession({ forceRefresh: false });
      console.log('Auth check - Session:', session);

      const isSignedIn = session?.tokens?.accessToken;
      console.log('Auth check - Is signed in:', isSignedIn?.toString());

      if (isSignedIn) {
        try {
          // Get current user details
          const currentUser = await getCurrentUser();
          console.log('Auth check - Current user:', currentUser);

          // Set up user details with admin check
          const isAdminUser = checkIsAdmin(session);
          const userDetails = {
            uid: currentUser.userId,
            email: currentUser.signInDetails?.loginId || '',
            isAdmin: isAdminUser,
            role: isAdminUser ? 'admin' : 'user',
          };

          setUserDetails(userDetails);
          setIsAuthenticated(true);
          setIsAdmin(userDetails.isAdmin);

          return true;
        } catch (userError) {
          console.error('Error getting current user:', userError);
          setIsAuthenticated(false);
          setIsAdmin(false);
          setUserDetails(null);
          return false;
        }
      } else {
        console.log('No active session found');
        setIsAuthenticated(false);
        setIsAdmin(false);
        setUserDetails(null);
        return false;
      }
    } catch (error) {
      console.error('Auth check error:', error);
      setIsAuthenticated(false);
      setIsAdmin(false);
      setUserDetails(null);
      return false;
    } finally {
      setLoading(false);
    }
  }, []); // Empty dependency array as we don't use any external values

  // Logout function
  const logout = async () => {
    try {
      await signOut({ global: true });
      // Clear all auth state
      setIsAuthenticated(false);
      setIsAdmin(false);
      setUserDetails(null);
    } catch (error) {
      console.error('Error during sign out:', error);
    }
  };

  // Initialize auth only once on mount
  useEffect(() => {
    let isMounted = true;

    const initAuth = async () => {
      if (hasInitialized.current) return;
      hasInitialized.current = true;

      try {
        console.log('AuthContext - Initializing auth...');
        await checkAuth();
      } catch (error) {
        console.error('Error initializing auth:', error);
        if (isMounted) {
          setIsAuthenticated(false);
          setIsAdmin(false);
          setUserDetails(null);
          setLoading(false);
        }
      }
    };

    if (typeof window !== 'undefined') {
      initAuth();
    }

    return () => {
      isMounted = false;
    };
  }, [checkAuth]); // Include checkAuth but prevent re-runs with hasInitialized

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        isAdmin,
        loading,
        userDetails,
        logout,
        checkAuth,
        fetchUserDetails,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
