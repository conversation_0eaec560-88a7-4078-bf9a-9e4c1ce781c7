'use client';

import {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
  useMemo,
  ReactNode,
} from 'react';
import '@/lib/amplify';
import { getCurrentUser, signOut } from 'aws-amplify/auth';
import { fetchAuthSession } from 'aws-amplify/auth';
import { UserDetails } from '@/services/userService';

interface AuthContextType {
  isAuthenticated: boolean;
  isAdmin: boolean;
  loading: boolean;
  userDetails: UserDetails | null;
  logout: () => void;
  checkAuth: () => Promise<boolean>;
  fetchUserDetails: () => Promise<UserDetails | null>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isAdmin, setIsAdmin] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [userDetails, setUserDetails] = useState<UserDetails | null>(null);
  // Using window.location for redirects to ensure clean state

  // Function to fetch user details from the API
  const fetchUserDetails = async (): Promise<UserDetails | null> => {
    try {
      const session = await fetchAuthSession();
      console.log('Auth check - Session:', session);
      if (!session.tokens?.accessToken) {
        return null;
      }

      // const token = session.tokens.accessToken.toString();
      // console.log('Auth check - Token:', token);
      const details = await getCurrentUser();

      return {
        uid: details.userId,
        email: details.signInDetails?.loginId || '',
        isAdmin: false,
        role: 'user',
      };
    } catch (error) {
      console.error('Error fetching user details:', error);
      return null;
    }
  };

  // Function to check authentication status
  const checkAuth = useCallback(async (): Promise<boolean> => {
    setLoading(true);

    try {
      // Get session from Amplify
      const session = await fetchAuthSession({ forceRefresh: false });
      console.log('Auth check - Session:', session);

      const isSignedIn = !!session?.tokens?.accessToken;
      console.log('Auth check - Is signed in:', isSignedIn);

      if (isSignedIn) {
        try {
          // Get current user details
          const currentUser = await getCurrentUser();
          console.log('Auth check - Current user:', currentUser);

          // Set up user details
          const userDetails = {
            uid: currentUser.userId,
            email: currentUser.signInDetails?.loginId || '',
            isAdmin: false, // You might want to fetch this from your user attributes
            role: 'user', // You might want to fetch this from your user attributes
          };

          setUserDetails(userDetails);
          setIsAuthenticated(true);
          setIsAdmin(userDetails.isAdmin);

          return true;
        } catch (userError) {
          console.error('Error getting current user:', userError);
          setIsAuthenticated(false);
          setIsAdmin(false);
          setUserDetails(null);
          return false;
        }
      } else {
        console.log('No active session found');
        setIsAuthenticated(false);
        setIsAdmin(false);
        setUserDetails(null);
        return false;
      }
    } catch (error) {
      console.error('Auth check error:', error);
      setIsAuthenticated(false);
      setIsAdmin(false);
      setUserDetails(null);
      return false;
    } finally {
      setLoading(false);
    }
  }, []); // Empty dependency array as we don't use any external values

  // Logout function
  const logout = async () => {
    try {
      await signOut({ global: true });
      // Clear all auth state
      setIsAuthenticated(false);
      setIsAdmin(false);
      setUserDetails(null);
      window.location.href = '/login';
    } catch (error) {
      console.error('Error during sign out:', error);
    }
  };

  // Memoize the checkAuth function to prevent unnecessary re-renders
  const memoizedCheckAuth = useMemo(() => checkAuth, [checkAuth]);

  useEffect(() => {
    let isMounted = true;

    const initAuth = async () => {
      try {
        await memoizedCheckAuth();
      } catch (error) {
        console.error('Error initializing auth:', error);
        if (isMounted) {
          setIsAuthenticated(false);
          setIsAdmin(false);
          setUserDetails(null);
        }
      }
    };

    if (typeof window !== 'undefined') {
      initAuth();
    }

    return () => {
      isMounted = false;
    };
  }, [memoizedCheckAuth]);

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        isAdmin,
        loading,
        userDetails,
        logout,
        checkAuth,
        fetchUserDetails,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
