import { useState } from 'react';
import { Shield, ShieldAlert, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface ChangeRoleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  staff: {
    id: number;
    name: string;
    currentRole: string;
  } | null;
  onRoleChange: (newRole: string) => void;
}

export function ChangeRoleDialog({
  open,
  onOpenChange,
  staff,
  onRoleChange,
}: ChangeRoleDialogProps) {
  const [selectedRole, setSelectedRole] = useState(staff?.currentRole || '');

  if (!staff) return null;

  const roles = [
    {
      id: 'master',
      name: 'Master Admin',
      description: 'Full access to all features and settings',
      icon: ShieldAlert,
      iconColor: 'text-rose-500',
    },
    {
      id: 'standard',
      name: 'Standard Admin',
      description: 'Limited access to features based on permissions',
      icon: Shield,
      iconColor: 'text-blue-500',
    },
  ];

  const handleRoleChange = (roleId: string, roleName: string) => {
    setSelectedRole(roleName);
  };

  const handleSave = () => {
    onRoleChange(selectedRole);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Change Role
          </DialogTitle>
          <p className="text-sm text-muted-foreground">
            Update the role for {staff.name}
          </p>
        </DialogHeader>

        <div className="space-y-4 py-2">
          <div className="space-y-2">
            {roles.map((role) => {
              const Icon = role.icon;
              const isSelected = selectedRole === role.name;

              return (
                <button
                  key={role.id}
                  type="button"
                  className={`w-full text-left p-4 rounded-lg border transition-colors ${
                    isSelected
                      ? 'border-rose-200 bg-rose-50 dark:border-rose-900 dark:bg-rose-950/50'
                      : 'border-border hover:bg-accent'
                  }`}
                  onClick={() => handleRoleChange(role.id, role.name)}
                >
                  <div className="flex items-center">
                    <div
                      className={`p-2 rounded-full mr-3 ${isSelected ? 'bg-rose-100 dark:bg-rose-900' : 'bg-muted'}`}
                    >
                      <Icon className={`h-5 w-5 ${role.iconColor}`} />
                    </div>
                    <div>
                      <div className="font-medium">{role.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {role.description}
                      </div>
                    </div>
                  </div>
                </button>
              );
            })}
          </div>

          <div className="rounded-lg bg-blue-50 p-4 text-sm text-blue-700 dark:bg-blue-900/30 dark:text-blue-300">
            <div className="flex">
              <Info className="mr-2 h-4 w-4 flex-shrink-0 mt-0.5" />
              <span>
                Changing the role will update the staff member&apos;s access
                permissions immediately.
              </span>
            </div>
          </div>

          <div className="flex justify-end pt-2">
            <Button
              variant="destructive"
              onClick={handleSave}
              disabled={!selectedRole || selectedRole === staff.currentRole}
            >
              Update Role
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
