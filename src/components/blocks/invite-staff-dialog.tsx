'use client';

import { useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Check, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { fetchAuthSession } from 'aws-amplify/auth';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { sendUserInvite } from '@/actions/user-invites';
import { UserInviteRequest } from '@/lib/types';

const formSchema = z.object({
  name: z.string().min(1, { message: 'Please enter the staff member\'s name' }),
  email: z.string().email({ message: 'Please enter a valid email address' }),
  role: z.enum(['staff', 'admin'], { message: 'Please select a role' }),
});

interface InviteStaffDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  clinicIds?: string[];
}

export function InviteStaffDialog({
  open,
  onOpenChange,
  clinicIds = [],
}: InviteStaffDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      email: '',
      role: 'staff' as const,
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsSubmitting(true);

    try {
      // Get token from Amplify auth session
      const session = await fetchAuthSession();
      const token = session.tokens?.accessToken?.toString();

      if (!token) {
        toast.error('Authentication Error', {
          description: 'Please log in again to send invitations.',
        });
        setIsSubmitting(false);
        return;
      }

      // Use provided clinic IDs or default ones for testing
      const defaultClinicIds = ['6821fde1a57e294a49fcbc4d', '682dedbe0bf795e11515c5da'];
      const inviteData: UserInviteRequest = {
        invitee_email: values.email,
        invitee_name: values.name,
        role: values.role,
        clinic_ids: clinicIds.length > 0 ? clinicIds : defaultClinicIds,
      };

      const result = await sendUserInvite(inviteData, token);

      if (result.ok && result.data) {
        toast.success('Invitation Sent', {
          description: `An invitation has been sent to ${values.email}`,
        });

        setIsSuccess(true);

        // Reset form after 2 seconds and close dialog
        setTimeout(() => {
          form.reset();
          setIsSuccess(false);
          onOpenChange(false);
        }, 2000);
      } else {
        toast.error('Failed to Send Invitation', {
          description: result.error || 'Please try again later.',
        });
      }
    } catch (error) {
      console.error('Error sending invitation:', error);
      toast.error('Failed to Send Invitation', {
        description: 'An unexpected error occurred. Please try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Invite Staff Member</DialogTitle>
          <DialogDescription>
            Send an invitation to a new staff member to join your clinic.
          </DialogDescription>
        </DialogHeader>

        {isSuccess ? (
          <Alert className="bg-emerald-50 border-emerald-200">
            <Check className="h-4 w-4 text-emerald-600" />
            <AlertTitle className="text-emerald-800">
              Invitation Sent!
            </AlertTitle>
            <AlertDescription className="text-emerald-700">
              An invitation has been sent to the email address. The staff member will receive instructions to join your clinic.
            </AlertDescription>
          </Alert>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name</FormLabel>
                    <FormControl>
                      <Input placeholder="John Doe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Role</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a role" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="staff">Staff</SelectItem>
                        <SelectItem value="admin">Admin</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    'Send Invitation'
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
}
