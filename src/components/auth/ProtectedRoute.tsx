'use client';

import { useEffect, ReactNode, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';

interface ProtectedRouteProps {
  children: ReactNode;
  requireAdmin?: boolean;
}

export default function ProtectedRoute({
  children,
  requireAdmin = false,
}: ProtectedRouteProps) {
  const { isAdmin, loading, checkAuth, isAuthenticated } = useAuth();
  const router = useRouter();
  const [isVerifying, setIsVerifying] = useState(true);

  useEffect(() => {
    let isMounted = true;

    const verifyAuth = async () => {
      if (loading) return;
      
      try {
        console.log('ProtectedRoute - Verifying authentication...');
        const isAuthed = await checkAuth();

        console.log('ProtectedRoute - Auth check result:', {
          isAuthed,
          isAdmin,
          requireAdmin,
        });

        if (!isAuthed) {
          console.log('ProtectedRoute - Not authenticated, redirecting to login');
          router.replace('/login');
          return;
        }


        // If admin access is required but user is not admin
        if (requireAdmin && !isAdmin) {
          console.log('ProtectedRoute - Admin access required but user is not admin');
          router.replace('/dashboard');
          return;
        }
      } catch (error) {
        console.error('ProtectedRoute - Error during auth verification:', error);
        router.replace('/login');
      } finally {
        if (isMounted) {
          setIsVerifying(false);
        }
      }
    };

    verifyAuth();

    return () => {
      isMounted = false;
    };
  }, [isAdmin, loading, requireAdmin, router, checkAuth, isAuthenticated]);

  // Show loading state while verifying auth
  if (isVerifying || loading) {
    return (
      <div className="flex min-h-screen w-full items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <div className="h-16 w-16 animate-spin rounded-full border-4 border-transparent border-t-purple-600">
            <div className="h-12 w-12 animate-spin rounded-full border-4 border-transparent border-t-primary"></div>
          </div>
          <p className="text-muted-foreground">Verifying authentication...</p>
        </div>
      </div>
    );
  }

  // If we're still authenticated but not an admin when admin is required
  if (isAuthenticated && requireAdmin && !isAdmin) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <div className="h-12 w-12 animate-spin rounded-full border-4 border-t-2 border-primary"></div>
          <p className="text-muted-foreground">Redirecting to dashboard...</p>
        </div>
      </div>
    );
  }

  // If authenticated (and admin if required), render the children
  if (isAuthenticated) {
    return <>{children}</>;
  }

  // Default fallback (should have been redirected to login already)
  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="flex flex-col items-center gap-4">
        <div className="h-12 w-12 animate-spin rounded-full border-4 border-t-2 border-primary"></div>
        <p className="text-muted-foreground">Redirecting to login...</p>
      </div>
    </div>
  );
}
