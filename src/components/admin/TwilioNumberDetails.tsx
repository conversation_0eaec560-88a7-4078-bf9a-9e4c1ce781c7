'use client';

import { TwilioPhoneNumber } from '@/lib/twilio-types';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Check, X, Phone, MessageSquare } from 'lucide-react';
import { format, parseISO } from 'date-fns';

interface TwilioNumberDetailsProps {
  number: TwilioPhoneNumber | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function TwilioNumberDetails({
  number,
  open,
  onOpenChange,
}: TwilioNumberDetailsProps) {
  const formatDate = (dateString: string) => {
    try {
      return format(parseISO(dateString), 'MMM dd, yyyy HH:mm:ss');
    } catch {
      return 'Invalid date';
    }
  };

  if (!number) {
    return null;
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold flex items-center">
            <Phone className="mr-2 h-5 w-5 text-primary" />
            {number.friendlyName}
          </DialogTitle>
          <DialogDescription className="text-base">
            {number.phoneNumber}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div className="flex flex-wrap gap-2">
            <Badge
              variant="outline"
              className={
                number.status === 'in-use'
                  ? 'bg-emerald-50 text-emerald-700 border-emerald-200'
                  : 'bg-amber-50 text-amber-700 border-amber-200'
              }
            >
              {number.status === 'in-use' ? (
                <>
                  <Check className="h-3 w-3 mr-1" /> Active
                </>
              ) : (
                <>
                  <X className="h-3 w-3 mr-1" /> Inactive
                </>
              )}
            </Badge>

            {number.capabilities.voice && (
              <Badge
                variant="outline"
                className="bg-blue-50 text-blue-700 border-blue-200"
              >
                <Phone className="h-3 w-3 mr-1" /> Voice
              </Badge>
            )}

            {number.capabilities.sms && (
              <Badge
                variant="outline"
                className="bg-green-50 text-green-700 border-green-200"
              >
                <MessageSquare className="h-3 w-3 mr-1" /> SMS
              </Badge>
            )}

            {number.capabilities.mms && (
              <Badge
                variant="outline"
                className="bg-purple-50 text-purple-700 border-purple-200"
              >
                MMS
              </Badge>
            )}
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2">Phone Number Details</h3>
            <Table>
              <TableBody>
                <TableRow>
                  <TableCell className="font-medium w-1/3">SID</TableCell>
                  <TableCell className="font-mono text-sm">
                    {number.sid}
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Account SID</TableCell>
                  <TableCell className="font-mono text-sm">
                    {number.accountSid}
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Date Created</TableCell>
                  <TableCell>{formatDate(number.dateCreated)}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Date Updated</TableCell>
                  <TableCell>{formatDate(number.dateUpdated)}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">
                    Address Requirements
                  </TableCell>
                  <TableCell className="capitalize">
                    {number.addressRequirements}
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Origin</TableCell>
                  <TableCell className="capitalize">{number.origin}</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2">Voice Configuration</h3>
            <Table>
              <TableBody>
                <TableRow>
                  <TableCell className="font-medium w-1/3">Voice URL</TableCell>
                  <TableCell className="break-all text-sm">
                    {number.voiceUrl}
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Voice Method</TableCell>
                  <TableCell>{number.voiceMethod}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">
                    Voice Fallback URL
                  </TableCell>
                  <TableCell className="break-all text-sm">
                    {number.voiceFallbackUrl}
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Status Callback</TableCell>
                  <TableCell className="break-all text-sm">
                    {number.statusCallback}
                  </TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">
                    Voice Receive Mode
                  </TableCell>
                  <TableCell className="capitalize">
                    {number.voiceReceiveMode}
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          {(number.capabilities.sms || number.capabilities.mms) && (
            <div>
              <h3 className="text-lg font-semibold mb-2">SMS Configuration</h3>
              <Table>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium w-1/3">SMS URL</TableCell>
                    <TableCell className="break-all text-sm">
                      {number.smsUrl}
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">SMS Method</TableCell>
                    <TableCell>{number.smsMethod}</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">
                      SMS Fallback URL
                    </TableCell>
                    <TableCell className="break-all text-sm">
                      {number.smsFallbackUrl || 'None'}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
