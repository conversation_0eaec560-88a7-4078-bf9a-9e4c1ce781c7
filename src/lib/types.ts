export interface ClinicType {
  _id: string;
  clinic_name: string;
  clinic_email: string;
  clinic_website: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  human_transfer_destination_number?: string;
  crm_details?: {
    name: string;
    auth_details?: Record<string, string>;
    custom_fields?: Record<string, string>;
  };
}

export interface KnowlegdeBaseType {
  data: {
    knowledge_base_id: string;
    knowledge_base_name: string;
    enable_auto_refresh: boolean;
    status: string;
    user_modified_timestamp: number;
  };
}

export interface CreateKnowledgeBaseInput {
  urls: string[];
  texts: {
    title: string;
    text: string;
  }[];
}

// User Invite Types
export interface UserInviteRequest {
  invitee_email: string;
  invitee_name: string;
  role: 'staff' | 'admin';
  clinic_ids: string[];
}

export interface UserInviteResponse {
  success: boolean;
  message: string;
  data?: {
    invite_id: string;
    token: string;
  };
}

export interface UserInviteDetails {
  token: string;
  invitee_email: string;
  invitee_name: string;
  role: string;
  clinic_ids: string[];
  inviter_email: string;
  created_at: string;
  status: 'pending' | 'accepted' | 'expired';
}

export interface AcceptInviteRequest {
  token: string;
}

export interface AcceptInviteResponse {
  success: boolean;
  message: string;
  data?: {
    user_id: string;
  };
}
